const { QdrantClient } = require('@qdrant/js-client-rest');
const embeddingService = require('./embeddingService');

class QdrantService {
  constructor() {
    this.client = null;
    this.collectionName = 'chatai_documents';
    this.isInitialized = false;
  }

  /**
   * Initialize Qdrant client
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      const clientConfig = {
        url: process.env.QDRANT_URL || 'http://localhost:6333',
      };

      // Add API key if provided
      if (process.env.QDRANT_API_KEY) {
        clientConfig.apiKey = process.env.QDRANT_API_KEY;
        console.log('🔐 Using API key authentication for Qdrant');
      } else {
        console.log('⚠️ No API key provided - connecting to Qdrant without authentication');
      }

      this.client = new QdrantClient(clientConfig);

      // Test connection
      await this.client.getCollections();
      this.isInitialized = true;
      console.log('✅ QdrantService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize QdrantService:', error.message);
      console.log('⚠️ Qdrant not available - vector search will use fallback mode');
      // Don't throw error, allow graceful fallback
      this.isInitialized = false;
    }
  }

  /**
   * Search for relevant document chunks using vector similarity
   * @param {string} query - Search query
   * @param {string} appId - Application ID for tenant isolation
   * @param {number} limit - Maximum number of results (default: 10)
   * @param {number} scoreThreshold - Minimum similarity score (default: 0.1)
   * @returns {Promise<Array>} Array of relevant document chunks
   */
  async searchSimilarChunks(query, appId, limit = 10, scoreThreshold = 0.1) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      console.log('⚠️ Qdrant not available, returning empty results for vector search');
      return [];
    }

    try {
      console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 VECTOR SEARCH IN QDRANT`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`📝 Query: "${query}"`);
      console.log(`🏢 AppId: ${appId}`);
      console.log(`📊 Limit: ${limit}`);
      console.log(`🎯 Score Threshold: ${scoreThreshold}`);

      // Step 1: Generate embeddings for the query
      console.log(`\n🔄 Step 1: Generating query embeddings...`);
      const queryEmbeddings = await embeddingService.generateEmbeddings(query);
      console.log(`✅ Query embeddings generated: ${queryEmbeddings.length} dimensions`);

      // Step 2: Perform vector search with tenant isolation
      console.log(`\n🔄 Step 2: Performing vector search...`);
      const searchStartTime = Date.now();

      const searchResult = await this.client.search(this.collectionName, {
        vector: queryEmbeddings,
        filter: {
          must: [
            {
              key: 'appId',
              match: {
                value: appId
              }
            }
          ]
        },
        limit: limit,
        score_threshold: scoreThreshold,
        with_payload: true,
        with_vector: false
      });

      const searchDuration = Date.now() - searchStartTime;
      console.log(`✅ Vector search completed in ${searchDuration}ms`);
      console.log(`📊 Found ${searchResult.length} similar chunks`);

      // Transform results to match expected format
      const results = searchResult.map((point, index) => {
        console.log(`📄 Result ${index + 1}: Score ${point.score.toFixed(4)} - "${point.payload.text.substring(0, 100)}..."`);
        return {
          id: point.id,
          text: point.payload.text,
          metadata: {
            documentId: point.payload.documentId,
            filename: point.payload.filename,
            chunkIndex: point.payload.chunkIndex,
            totalChunks: point.payload.totalChunks,
            appId: point.payload.appId
          },
          score: point.score
        };
      });

      console.log(`✅ Vector search returned ${results.length} relevant chunks`);

      if (results.length > 0) {
        console.log(`🎯 Best match score: ${results[0].score.toFixed(4)}`);
        console.log(`📄 Best match text: "${results[0].text.substring(0, 200)}..."`);
      }

      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

      return results;

    } catch (error) {
      console.error('❌ Qdrant search error:', error.message);
      throw error;
    }
  }

  /**
   * Get all document chunks for a specific appId
   * @param {string} appId - Application ID
   * @param {number} limit - Maximum number of results
   * @returns {Promise<Array>} Array of document chunks
   */
  async getAllDocumentChunks(appId, limit = 50) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      console.log('⚠️ Qdrant not available, returning empty results for document chunks');
      return [];
    }

    try {
      console.log(`📄 Getting all document chunks for appId: ${appId}`);

      const searchResult = await this.client.scroll(this.collectionName, {
        filter: {
          must: [
            {
              key: 'appId',
              match: {
                value: appId
              }
            }
          ]
        },
        limit: limit,
        with_payload: true,
        with_vector: false
      });

      console.log(`📊 Retrieved ${searchResult.points.length} total chunks for appId: ${appId}`);

      return searchResult.points.map(point => ({
        id: point.id,
        text: point.payload.text,
        metadata: {
          documentId: point.payload.documentId,
          filename: point.payload.filename,
          chunkIndex: point.payload.chunkIndex,
          totalChunks: point.payload.totalChunks,
          appId: point.payload.appId
        }
      }));

    } catch (error) {
      console.error('❌ Failed to get document chunks:', error.message);
      throw error;
    }
  }

  /**
   * Store document chunk with embeddings in Qdrant (single chunk)
   * @param {Object} document - Document object with text and metadata
   * @param {number[]} embeddings - Vector embeddings
   * @returns {Promise<string>} Point ID
   */
  async storeDocument(document, embeddings) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('Qdrant not available for document storage');
    }

    try {
      // Ensure collection exists with correct dimensions
      await this.ensureCollection(embeddings.length);

      // Generate unique numeric point ID (Qdrant prefers numeric IDs)
      const pointId = Date.now() + document.metadata.chunkIndex;

      // Store the document chunk
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: [
          {
            id: pointId,
            vector: embeddings,
            payload: {
              text: document.text,
              appId: document.metadata.appId,
              documentId: document.metadata.documentId,
              filename: document.metadata.filename,
              userId: document.metadata.userId,
              chunkIndex: document.metadata.chunkIndex,
              totalChunks: document.metadata.totalChunks,
              timestamp: new Date().toISOString(),
              ...document.metadata
            }
          }
        ]
      });

      console.log(`✅ Stored document chunk with ID: ${pointId}`);
      return pointId;

    } catch (error) {
      console.error('❌ Failed to store document in Qdrant:', error.message);
      throw error;
    }
  }

  /**
   * Store multiple document chunks in Qdrant vector database (optimized batch operation)
   * @param {Array} documentsWithEmbeddings - Array of {document, embeddings} objects
   * @param {number} batchSize - Number of chunks to store per batch (default: 50)
   * @returns {Promise<Object>} Result with stored and failed chunks
   */
  async storeDocumentsBatch(documentsWithEmbeddings, batchSize = 50) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('Qdrant not available for batch document storage');
    }

    if (!documentsWithEmbeddings || documentsWithEmbeddings.length === 0) {
      return { storedChunks: [], failedChunks: [], totalProcessed: 0 };
    }

    console.log(`🚀 Starting optimized batch storage for ${documentsWithEmbeddings.length} chunks`);
    console.log(`📦 Batch size: ${batchSize} chunks per operation`);

    // Ensure collection exists with correct dimensions
    const firstEmbedding = documentsWithEmbeddings.find(item => item.embeddings)?.embeddings;
    if (firstEmbedding) {
      await this.ensureCollection(firstEmbedding.length);
    }

    const storedChunks = [];
    const failedChunks = [];
    const totalBatches = Math.ceil(documentsWithEmbeddings.length / batchSize);

    for (let i = 0; i < documentsWithEmbeddings.length; i += batchSize) {
      const batch = documentsWithEmbeddings.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      console.log(`📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} chunks)`);

      try {
        const result = await this.processBatchStorage(batch, i);
        storedChunks.push(...result.stored);
        failedChunks.push(...result.failed);

        console.log(`✅ Batch ${batchNumber}/${totalBatches} completed: ${result.stored.length} stored, ${result.failed.length} failed`);

      } catch (error) {
        console.error(`❌ Batch ${batchNumber} failed completely:`, error.message);

        // Mark all chunks in this batch as failed
        batch.forEach((item, index) => {
          failedChunks.push({
            chunkIndex: i + index,
            reason: `Batch storage failed: ${error.message}`,
            document: item.document
          });
        });
      }
    }

    console.log(`\n📊 Batch storage summary:`);
    console.log(`✅ Successfully stored: ${storedChunks.length}/${documentsWithEmbeddings.length} chunks`);
    console.log(`❌ Failed to store: ${failedChunks.length}/${documentsWithEmbeddings.length} chunks`);

    return {
      storedChunks,
      failedChunks,
      totalProcessed: documentsWithEmbeddings.length,
      successRate: Math.round((storedChunks.length / documentsWithEmbeddings.length) * 100)
    };
  }

  /**
   * Process a single batch of document storage
   * @param {Array} batch - Batch of {document, embeddings} objects
   * @param {number} startIndex - Starting index for this batch
   * @returns {Promise<Object>} Result with stored and failed items
   */
  async processBatchStorage(batch, startIndex) {
    const stored = [];
    const failed = [];
    const timestamp = new Date().toISOString();

    // Filter out items with null embeddings
    const validItems = batch.filter((item, index) => {
      if (!item.embeddings) {
        failed.push({
          chunkIndex: startIndex + index,
          reason: 'No embeddings available',
          document: item.document
        });
        return false;
      }
      return true;
    });

    if (validItems.length === 0) {
      return { stored, failed };
    }

    // Prepare points for batch upsert
    const points = validItems.map((item, index) => {
      const actualIndex = batch.indexOf(item);
      const pointId = Date.now() + startIndex + actualIndex + Math.random() * 1000; // Ensure uniqueness

      return {
        id: Math.floor(pointId),
        vector: item.embeddings,
        payload: {
          text: item.document.text,
          appId: item.document.metadata.appId,
          documentId: item.document.metadata.documentId,
          filename: item.document.metadata.filename,
          userId: item.document.metadata.userId,
          chunkIndex: item.document.metadata.chunkIndex,
          totalChunks: item.document.metadata.totalChunks,
          timestamp: timestamp,
          ...item.document.metadata
        }
      };
    });

    try {
      // Perform batch upsert
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: points
      });

      // Mark all as successfully stored
      validItems.forEach((item, index) => {
        const actualIndex = batch.indexOf(item);
        stored.push({
          chunkIndex: startIndex + actualIndex,
          pointId: points[index].id,
          textLength: item.document.text.length,
          chunkType: item.document.metadata.chunkType || 'unknown',
          pageNumber: item.document.metadata.pageNumber || null
        });
      });

    } catch (error) {
      console.error(`❌ Batch upsert failed:`, error.message);

      // Mark all valid items as failed
      validItems.forEach((item) => {
        const actualIndex = batch.indexOf(item);
        failed.push({
          chunkIndex: startIndex + actualIndex,
          reason: `Batch upsert failed: ${error.message}`,
          document: item.document
        });
      });
    }

    return { stored, failed };
  }

  /**
   * Ensure collection exists with correct vector dimensions
   * @param {number} dimensions - Vector dimensions
   */
  async ensureCollection(dimensions) {
    try {
      // Check if collection exists
      const collections = await this.client.getCollections();
      const collectionExists = collections.collections.some(c => c.name === this.collectionName);

      if (!collectionExists) {
        console.log(`🔧 Creating Qdrant collection: ${this.collectionName} with ${dimensions} dimensions`);

        await this.client.createCollection(this.collectionName, {
          vectors: {
            size: dimensions,
            distance: 'Cosine'
          }
        });

        console.log(`✅ Created Qdrant collection: ${this.collectionName}`);
      } else {
        // Verify dimensions match
        const collectionInfo = await this.client.getCollection(this.collectionName);
        const existingDimensions = collectionInfo.config.params.vectors.size;

        if (existingDimensions !== dimensions) {
          console.warn(`⚠️ Collection dimension mismatch: existing=${existingDimensions}, required=${dimensions}`);
          console.log(`🔧 Recreating collection with correct dimensions...`);

          // Delete and recreate collection
          await this.client.deleteCollection(this.collectionName);
          await this.client.createCollection(this.collectionName, {
            vectors: {
              size: dimensions,
              distance: 'Cosine'
            }
          });

          console.log(`✅ Recreated collection with ${dimensions} dimensions`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to ensure collection:', error.message);
      throw error;
    }
  }

  /**
   * Delete document chunks by documentId and appId
   * @param {string} documentId - Document ID
   * @param {string} appId - Application ID
   * @returns {Promise<number>} Number of deleted chunks
   */
  async deleteDocument(documentId, appId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('Qdrant not available for document deletion');
    }

    try {
      console.log(`🗑️ Deleting document chunks: ${documentId} for app: ${appId}`);

      // Delete all chunks for this document
      const deleteResult = await this.client.delete(this.collectionName, {
        filter: {
          must: [
            {
              key: 'documentId',
              match: { value: documentId }
            },
            {
              key: 'appId',
              match: { value: appId }
            }
          ]
        }
      });

      console.log(`✅ Deleted document chunks: ${deleteResult.operation_id}`);
      return deleteResult.operation_id ? 1 : 0; // Return count estimate

    } catch (error) {
      console.error('❌ Failed to delete document:', error.message);
      throw error;
    }
  }

  /**
   * Get app statistics
   * @param {string} appId - Application ID
   * @returns {Promise<Object>} App statistics
   */
  async getAppStats(appId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      return { status: 'error', message: 'Qdrant not available' };
    }

    try {
      const chunks = await this.getAllDocumentChunks(appId, 1000);
      const collectionInfo = await this.getCollectionInfo();

      return {
        status: 'healthy',
        appId: appId,
        totalChunks: chunks.length,
        collection: {
          name: this.collectionName,
          vectorDimensions: collectionInfo.config.params.vectors.size,
          distance: collectionInfo.config.params.vectors.distance
        }
      };

    } catch (error) {
      console.error('❌ Failed to get app stats:', error.message);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Get collection info
   * @returns {Promise<Object>} Collection information
   */
  async getCollectionInfo() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const info = await this.client.getCollection(this.collectionName);
      return info;
    } catch (error) {
      console.error('❌ Failed to get collection info:', error.message);
      throw error;
    }
  }
}

module.exports = new QdrantService();
