require('dotenv').config();

const config = {
  // Server Configuration
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',

  // User Service Configuration
  userService: {
    url: process.env.USER_SERVICE_URL || 'http://localhost:3000',
  },

  // ChatAI Origin for key validation
  chatAiOrigin: process.env.CHATAI_ORIGIN || 'http://localhost:3001',

  // Qdrant Vector Database Configuration
  qdrant: {
    url: process.env.QDRANT_URL || 'http://localhost:6333',
    apiKey: process.env.QDRANT_API_KEY || null,
    collectionName: process.env.QDRANT_COLLECTION || 'chatai_documents',
  },

  // LlamaIndex Cloud Configuration (kept for fallback)
  llamaIndex: {
    apiKey: process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp',
    baseUrl: 'https://api.cloud.llamaindex.ai/api/v1',
  },

  // OpenRouter Configuration
  openRouter: {
    apiKey: process.env.OPENROUTER_API_KEY || '',
    baseUrl: 'https://openrouter.ai/api/v1',
    model: 'deepseek/deepseek-chat-v3-0324:free',
  },

  // OpenAI Configuration (for embeddings)
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    embeddingModel: 'text-embedding-ada-002',
    baseUrl: 'https://api.openai.com/v1',
  },

  // Cache Configuration
  cache: {
    ttlMinutes: parseInt(process.env.CACHE_TTL_MINUTES) || 15,
    maxSessions: parseInt(process.env.MAX_SESSIONS) || 1000,
    cleanupIntervalMinutes: parseInt(process.env.CLEANUP_INTERVAL_MINUTES) || 5,
  },

  // Rate Limiting
  rateLimit: {
    windowMinutes: parseInt(process.env.RATE_LIMIT_WINDOW_MINUTES) || 15,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  },

  // Logging
  logLevel: process.env.LOG_LEVEL || 'info',
};

// Validation
const requiredEnvVars = [
  'LLAMA_CLOUD_API_KEY',
  'OPENROUTER_API_KEY',
];

// Optional but recommended for semantic search
const recommendedEnvVars = [
  'OPENAI_API_KEY',
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars);
  process.exit(1);
}

const missingRecommended = recommendedEnvVars.filter(varName => !process.env[varName]);
if (missingRecommended.length > 0) {
  console.warn('⚠️ Missing recommended environment variables for semantic search:', missingRecommended);
  console.warn('💡 Without OPENAI_API_KEY, the system will use mock embeddings');
}

module.exports = config;
