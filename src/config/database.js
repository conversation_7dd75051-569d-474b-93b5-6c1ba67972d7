// Database configuration for ChatAI-SDK-Clean
require('dotenv').config();

const databaseConfig = {
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
  username: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DB || 'abstraxn',
  
  // Connection pool settings
  extra: {
    max: 20, // Maximum number of connections
    min: 5,  // Minimum number of connections
    acquire: 30000, // Maximum time to get connection
    idle: 10000,    // Maximum time connection can be idle
  },
  
  // TypeORM settings
  synchronize: false, // Don't auto-sync schema (User-Service manages this)
  logging: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],
  logger: 'simple-console',
  
  // Connection options
  options: {
    encrypt: process.env.NODE_ENV === 'production',
    trustServerCertificate: process.env.NODE_ENV !== 'production',
  },
  
  // Retry connection settings
  retryAttempts: 3,
  retryDelay: 3000,
  autoLoadEntities: true,
  
  // Entities will be loaded dynamically
  entities: [],
};

// Validate required environment variables
function validateDatabaseConfig() {
  const requiredVars = ['POSTGRES_HOST', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_DB'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('⚠️ Missing database environment variables:', missingVars);
    console.warn('Using default values for development');
  }
  
  console.log('📊 Database Configuration:');
  console.log(`   Host: ${databaseConfig.host}:${databaseConfig.port}`);
  console.log(`   Database: ${databaseConfig.database}`);
  console.log(`   User: ${databaseConfig.username}`);
  console.log(`   Logging: ${databaseConfig.logging}`);
}

module.exports = {
  databaseConfig,
  validateDatabaseConfig
};
