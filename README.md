# ChatAI

npm i 
npm run dev 


-- 
running vectorDB 
services:
  qdrant:
    image: qdrant/qdrant:v1.14.1
    container_name: qdrant
    restart: unless-stopped
    ports:
      - '127.0.0.1:6333:6333' # HTTP API - localhost only
      - '127.0.0.1:6334:6334' # gRPC API - localhost only
    volumes:
      - qdrant_storage:/qdrant/storage
    env_file:
      - .env.docker
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      # API Key for authentication - loaded from .env.docker file
      QDRANT__SERVICE__API_KEY: ${QDRANT_API_KEY}
      # Optional: Read-only API key for applications that only need read access
      # QDRANT__SERVICE__READ_ONLY_API_KEY: ${QDRANT_READ_ONLY_API_KEY}
    networks:
      - chatai_internal

volumes:
  qdrant_storage:
    driver: local

networks:
  chatai_internal:
    driver: bridge
    internal: true # Complete isolation (no internet access)
    ipam:
      config:
        - subnet: **********/16
