# ChatAI SDK Clean

A simplified, optimized version of the ChatAI SDK with minimal endpoints and maximum performance.

## Endpoints

### Main Chat Endpoint
```
GET /api/v1/?apikey={API_KEY}&query={USER_QUERY}&stream=true

**Parameters:**
- `apikey` (required): Your ChatAI API key
- `query` (required): User's question/query
- `sessionId` (optional): Session ID for conversation continuity
- `stream` (optional): `true` for streaming response, `false` for complete response (default: `true`)

### Health Check
```
GET /health
```

## Installation

1. **Install dependencies:**
```bash
npm install