# Chat AI Service

A simplified ChatAI SDK with streaming capabilities, vector database integration, and optimized performance.

## Features

- 🚀 Real-time streaming chat responses
- 🔍 Vector database integration with Qdrant
- 📚 Document processing and RAG (Retrieval Augmented Generation)
- 🛡️ Rate limiting and security middleware
- 📊 Comprehensive logging and monitoring
- 🔧 Smart chunking for document processing

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- Qdrant vector database
- API keys for OpenRouter/OpenAI and LlamaIndex Cloud

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd chat-ai-service
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

- `PORT`: Server port (default: 3001)
- `QDRANT_URL`: Qdrant vector database URL
- `QDRANT_API_KEY`: Your Qdrant API key
- `OPENROUTER_API_KEY`: OpenRouter API key for chat responses
- `LLAMA_CLOUD_API_KEY`: LlamaIndex Cloud API key
- Other configuration options as needed

### Running the Server

#### Development Mode

```bash
npm run dev
```

#### Production Mode

```bash
npm start
```

The server will start on `http://localhost:3001` (or your configured PORT).

## API Endpoints

### Streaming Chat

The main streaming endpoint for chat interactions:

**URL**: `http://localhost:3001/streaming-ui.html?apikey=YOUR_API_KEY`

**Method**: GET (for UI) / POST (for API calls)

**Example Usage**:

1. Open the streaming UI in your browser:

   ```
   http://localhost:3001/streaming-ui.html?apikey=test_api_key_1752470355743_kshejjgq3
   ```

2. Enter your prompt (e.g., "explain policies") and see real-time streaming responses with markdown formatting.

### API Documentation

Visit `http://localhost:3001/api-docs` for complete Swagger API documentation.

## Vector Database (Qdrant)

This service uses Qdrant as the vector database for document storage and retrieval:

- **Collection**: `chatai_documents` (configurable via `QDRANT_COLLECTION`)
- **URL**: Default `http://localhost:6333`
- **Purpose**: Stores document embeddings for RAG functionality

### Setting up Qdrant

1. **Using Docker**:

```bash
docker run -p 6333:6333 qdrant/qdrant
```

2. **Using Docker Compose** (if included in your setup):

```bash
docker-compose up qdrant
```

3. **Configure in .env**:

```env
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_secure_api_key_here
QDRANT_COLLECTION=chatai_documents
```

## Configuration

Key configuration options in `.env`:

| Variable                  | Description             | Default               |
| ------------------------- | ----------------------- | --------------------- |
| `PORT`                    | Server port             | 3001                  |
| `QDRANT_URL`              | Qdrant database URL     | http://localhost:6333 |
| `QDRANT_API_KEY`          | Qdrant API key          | -                     |
| `OPENROUTER_API_KEY`      | OpenRouter API key      | -                     |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100                   |
| `CACHE_TTL_MINUTES`       | Cache TTL in minutes    | 15                    |

## Testing the Streaming Feature

1. Start the server: `npm run dev`
2. Open: `http://localhost:3001/streaming-ui.html?apikey=test_api_key_1752470355743_kshejjgq3`
3. Enter a prompt like "explain policies"
4. Watch the real-time streaming response with markdown formatting (numbered lists, etc.)

## Logging

Logs are stored in the `logs/` directory:

- `logs/general/` - General application logs
- `logs/document-processing/` - Document processing specific logs

## Development

- Use `npm run dev` for development with auto-reload
- Check `logs/` directory for debugging information
- API documentation available at `/api-docs`

## License

MIT
