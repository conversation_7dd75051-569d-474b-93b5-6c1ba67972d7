<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant - Intelligent Chat Interface</title>
    <!-- MathJax for LaTeX formula rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* Embedded mode styles */
        body.embedded {
            background: #f8f9fa;
            padding: 0;
            min-height: 100vh;
        }

        .chat-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            height: 93vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .embedded .chat-container {
            border-radius: 0;
            box-shadow: none;
            height: 100vh;
            max-width: none;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .embedded .chat-header {
            padding: 15px 20px;
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .embedded .chat-header h1 {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .embedded .chat-header p {
            font-size: 12px;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .embedded .messages-container {
            padding: 15px;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .user .message-avatar {
            background: #667eea;
            color: white;
        }

        .bot .message-avatar {
            background: #28a745;
            color: white;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        /* Formatting styles for bot responses */
        .bot .message-content h1,
        .bot .message-content h2,
        .bot .message-content h3,
        .bot .message-content h4,
        .bot .message-content h5,
        .bot .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: 600;
            color: #2c3e50;
        }

        .bot .message-content h1 {
            font-size: 1.4em;
        }

        .bot .message-content h2 {
            font-size: 1.3em;
        }

        .bot .message-content h3 {
            font-size: 1.2em;
        }

        .bot .message-content h4 {
            font-size: 1.1em;
        }

        .bot .message-content p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .bot .message-content ul,
        .bot .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .bot .message-content li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .bot .message-content strong,
        .bot .message-content b {
            font-weight: 600;
            color: #2c3e50;
        }

        .bot .message-content em,
        .bot .message-content i {
            font-style: italic;
            color: #555;
        }

        .bot .message-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e83e8c;
        }

        .bot .message-content pre {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
            border-left: 4px solid #667eea;
        }

        .bot .message-content pre code {
            background: none;
            padding: 0;
            color: #333;
        }

        .bot .message-content blockquote {
            border-left: 4px solid #667eea;
            padding-left: 12px;
            margin: 8px 0;
            color: #666;
            font-style: italic;
        }

        /* Math formula styling */
        .bot .message-content .MathJax {
            margin: 8px 0;
            overflow-x: auto;
            max-width: 100%;
        }

        /* Display math (block equations) */
        .bot .message-content p:has(.MathJax_Display) {
            overflow-x: auto;
            padding: 8px 0;
            text-align: center;
        }

        /* Formula container */
        .formula-container {
            overflow-x: auto;
            margin: 12px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: center;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {

            0%,
            80%,
            100% {
                transform: scale(0.8);
                opacity: 0.5;
            }

            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .embedded .input-container {
            padding: 15px;
        }

        .input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 24px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
            max-height: 120px;
            min-height: 48px;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        #clearSessionButton {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
            font-size: 16px;
            margin-right: 8px;
        }

        #clearSessionButton:hover:not(:disabled) {
            background: #c82333;
            transform: scale(1.05);
        }

        #clearSessionButton:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        #sendButton {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        #sendButton:hover:not(:disabled) {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 8px 16px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .status.retrying {
            background: #cce5ff;
            color: #004085;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        /* Blinking cursor styles removed */

        /* Error message styling */
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            color: #721c24;
        }

        .error-message .error-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .error-message .error-details {
            font-size: 0.9em;
            opacity: 0.8;
        }

        /* Retry button styles removed */

        /* Scrollbar styling */
        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Theme variations */
        .theme-dark {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        .theme-dark .chat-container {
            background: #2c3e50;
            color: #ecf0f1;
        }

        .theme-dark .chat-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        }

        .theme-dark .messages-container {
            background: #34495e;
        }

        .theme-dark .bot .message-content {
            background: #3a4a5c;
            border-color: #4a5a6c;
            color: #ecf0f1;
        }

        .theme-dark .input-container {
            background: #2c3e50;
            border-top-color: #4a5a6c;
        }

        .theme-dark #messageInput {
            background: #3a4a5c;
            border-color: #4a5a6c;
            color: #ecf0f1;
        }

        .theme-dark #messageInput::placeholder {
            color: #95a5a6;
        }

        .theme-professional {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        .theme-professional .chat-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        .theme-professional .user .message-content {
            background: #1e3c72;
        }

        .theme-professional #sendButton {
            background: #1e3c72;
        }

        .theme-professional #sendButton:hover:not(:disabled) {
            background: #2a5298;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            body:not(.embedded) {
                padding: 5vh 10px;
            }

            .chat-container {
                height: 90vh;
                border-radius: 12px;
            }

            .embedded .chat-container {
                border-radius: 0;
                height: 100vh;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>

<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1 id="chatTitle">AI Assistant</h1>
            <p id="chatSubtitle">Intelligent conversations with real-time streaming responses</p>
        </div>

        <div class="messages-container" id="messagesContainer">
            <div class="message bot">
                <div class="message-avatar">🤖</div>
                <div class="message-content" id="welcomeMessage">
                    Hello! I'm your AI assistant. I can help you with questions, provide information, and assist with
                    various tasks. Ask me anything and I'll provide real-time streaming responses.
                </div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-form">
                <div class="input-wrapper">
                    <textarea id="messageInput" placeholder="Ask me anything..." rows="1"></textarea>
                </div>
                <button id="clearSessionButton" type="button" title="Clear conversation memory">
                    🗑️
                </button>
                <button id="sendButton" type="button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                    </svg>
                </button>
            </div>
        </div>

        <div class="status" id="status">Ready to chat • Connected to AI Service</div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const API_BASE = window.location.origin + '/api/v1/';
        const API_KEY = urlParams.get('apikey') || 'test_api_key_1752470355743_kshejjgq3';
        const EMBEDDED = urlParams.get('embedded') === 'true';

        // Configuration options from URL parameters
        const CONFIG = {
            title: urlParams.get('title') || 'AI Assistant',
            subtitle: urlParams.get('subtitle') || 'Intelligent conversations with real-time streaming responses',
            welcomeMessage: urlParams.get('welcome') || 'Hello! I\'m your AI assistant. I can help you with questions, provide information, and assist with various tasks. Ask me anything and I\'ll provide real-time streaming responses.',
            placeholder: urlParams.get('placeholder') || 'Ask me anything...',
            theme: urlParams.get('theme') || 'default',
            showSessionInfo: urlParams.get('showSession') !== 'false',
            serviceName: urlParams.get('serviceName') || 'AI Service'
        };

        // Apply embedded mode if specified
        if (EMBEDDED) {
            document.body.classList.add('embedded');
        }

        // Apply configuration
        function applyConfiguration() {
            // Update title and subtitle
            document.getElementById('chatTitle').textContent = CONFIG.title;
            document.getElementById('chatSubtitle').textContent = CONFIG.subtitle;
            document.getElementById('welcomeMessage').textContent = CONFIG.welcomeMessage;
            document.getElementById('messageInput').placeholder = CONFIG.placeholder;
            document.title = CONFIG.title + ' - Intelligent Chat Interface';

            // Apply theme if specified
            if (CONFIG.theme !== 'default') {
                document.body.classList.add(`theme-${CONFIG.theme}`);
            }
        }

        let isStreaming = false;
        let currentStreamingMessage = null;
        let sessionId = null; // Track session ID for conversation memory
        let streamingTimeout = null; // Track streaming timeout
        let retryCount = 0; // Track retry attempts
        const MAX_RETRIES = 3;
        const STREAMING_TIMEOUT = 30000; // 30 seconds timeout for streaming

        // DOM elements
        const messagesContainer = document.getElementById('messagesContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');

        // Auto-resize textarea
        messageInput.addEventListener('input', function () {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new lines)
        messageInput.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', sendMessage);

        function updateStatus(message, type = 'normal') {
            // Include session info if available and configured
            const sessionInfo = (CONFIG.showSessionInfo && sessionId) ? ` • Session: ${sessionId.substring(0, 8)}...` : '';

            // Add connection indicator
            const connectionIndicator = type === 'error' ? ' 🔴' :
                type === 'warning' ? ' 🟡' :
                    type === 'retrying' ? ' 🔄' : ' 🟢';

            status.textContent = message + sessionInfo + connectionIndicator;
            status.className = `status ${type}`;

            // Add helpful hints for error states
            if (type === 'error' && !message.includes('Ctrl+R')) {
                status.title = 'Press Ctrl+R (or Cmd+R) to reset error state';
            } else {
                status.title = '';
            }
        }

        function addMessage(content, type = 'bot', isStreaming = false, isHtml = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? '👤' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            if (isHtml && type === 'bot') {
                contentDiv.innerHTML = content;
            } else {
                contentDiv.textContent = content;
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);

            messagesContainer.appendChild(messageDiv);
            scrollToBottom();

            if (isStreaming) {
                currentStreamingMessage = contentDiv;
            }

            return contentDiv;
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot';
            typingDiv.id = 'typing-indicator';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';

            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.style.display = 'flex';
            indicator.innerHTML = `
                <span>AI is thinking</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;

            typingDiv.appendChild(avatar);
            typingDiv.appendChild(indicator);
            messagesContainer.appendChild(typingDiv);
            scrollToBottom();

            return typingDiv;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function scrollToBottom() {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function formatMarkdownToHtml(text) {
            // Simpler approach: directly handle LaTeX formulas

            // First, escape HTML to prevent XSS
            text = text.replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');

            // Process markdown formatting
            // Bold text **text** or __text__
            text = text.replace(/\*\*([^\*]+)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/__([^_]+)__/g, '<strong>$1</strong>');

            // Italic text *text* or _text_
            text = text.replace(/\*([^\*]+)\*/g, '<em>$1</em>');
            text = text.replace(/_([^_]+)_/g, '<em>$1</em>');

            // Code blocks ```code```
            text = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

            // Inline code `code`
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Headers
            text = text.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            text = text.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            text = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');

            // Process paragraphs and lists
            const lines = text.split('\n');
            let result = '';
            let inList = false;
            let currentParagraph = '';

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                if (line === '') {
                    if (currentParagraph) {
                        result += `<p>${currentParagraph}</p>`;
                        currentParagraph = '';
                    }
                    if (inList) {
                        result += inList === 'ul' ? '</ul>' : '</ol>';
                        inList = false;
                    }
                } else if (line.startsWith('- ') || line.startsWith('* ')) {
                    if (currentParagraph) {
                        result += `<p>${currentParagraph}</p>`;
                        currentParagraph = '';
                    }
                    if (!inList || inList !== 'ul') {
                        if (inList) result += '</ol>';
                        result += '<ul>';
                        inList = 'ul';
                    }
                    result += `<li>${line.substring(2)}</li>`;
                } else if (line.match(/^\d+\. /)) {
                    if (currentParagraph) {
                        result += `<p>${currentParagraph}</p>`;
                        currentParagraph = '';
                    }
                    if (!inList || inList !== 'ol') {
                        if (inList) result += '</ul>';
                        result += '<ol>';
                        inList = 'ol';
                    }
                    result += `<li>${line.replace(/^\d+\. /, '')}</li>`;
                } else if (line.startsWith('#')) {
                    if (currentParagraph) {
                        result += `<p>${currentParagraph}</p>`;
                        currentParagraph = '';
                    }
                    if (inList) {
                        result += inList === 'ul' ? '</ul>' : '</ol>';
                        inList = false;
                    }
                    result += line; // Headers are already processed above
                } else {
                    if (inList) {
                        result += inList === 'ul' ? '</ul>' : '</ol>';
                        inList = false;
                    }
                    if (currentParagraph) {
                        currentParagraph += ' ' + line;
                    } else {
                        currentParagraph = line;
                    }
                }
            }

            // Close any remaining elements
            if (currentParagraph) {
                result += `<p>${currentParagraph}</p>`;
            }
            if (inList) {
                result += inList === 'ul' ? '</ul>' : '</ol>';
            }

            return result;
        }

        function setInputState(disabled) {
            messageInput.disabled = disabled;
            sendButton.disabled = disabled;
            isStreaming = disabled;
        }

        function clearSession() {
            sessionId = null;
            console.log('Session cleared');
            updateStatus('Session cleared • Ready for new conversation', 'success');
            setTimeout(() => {
                updateStatus(`Ready to chat • Connected to ${CONFIG.serviceName}`, 'normal');
            }, 2000);
        }

        function clearStreamingTimeout() {
            if (streamingTimeout) {
                clearTimeout(streamingTimeout);
                streamingTimeout = null;
            }
        }

        function setStreamingTimeout(callback) {
            clearStreamingTimeout();
            streamingTimeout = setTimeout(callback, STREAMING_TIMEOUT);
        }

        function getErrorMessage(error, context = '') {
            let message = 'An unexpected error occurred';

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                message = 'Network connection failed. Please check your internet connection.';
            } else if (error.name === 'AbortError') {
                message = 'Request was cancelled or timed out.';
            } else if (error.message.includes('500')) {
                message = 'Server error (500). The AI service is temporarily unavailable.';
            } else if (error.message.includes('502') || error.message.includes('503') || error.message.includes('504')) {
                message = 'Server error. The service is temporarily unavailable.';
            } else if (error.message.includes('429')) {
                message = 'Rate limit exceeded. Please wait a moment before trying again.';
            } else if (error.message.includes('401') || error.message.includes('403')) {
                message = 'Authentication failed. Please check your API key.';
            } else if (error.message.includes('timeout')) {
                message = 'Request timed out. The server took too long to respond.';
            } else if (error.message) {
                message = error.message;
            }

            return context ? `${context}: ${message}` : message;
        }

        function isServerError(error) {
            return error.message.includes('500') ||
                error.message.includes('502') ||
                error.message.includes('503') ||
                error.message.includes('504') ||
                error.message.includes('Internal Server Error') ||
                error.message.includes('Bad Gateway') ||
                error.message.includes('Service Unavailable') ||
                error.message.includes('Gateway Timeout') ||
                (error.name === 'TypeError' && error.message.includes('fetch'));
        }

        function addErrorMessage(title, details) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '⚠️';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'error-title';
            titleDiv.textContent = title;

            const detailsDiv = document.createElement('div');
            detailsDiv.className = 'error-details';
            detailsDiv.textContent = details;

            errorDiv.appendChild(titleDiv);
            errorDiv.appendChild(detailsDiv);

            // Retry button functionality removed

            contentDiv.appendChild(errorDiv);
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);

            messagesContainer.appendChild(messageDiv);
            scrollToBottom();

            return messageDiv;
        }

        function addServerErrorMessage(userMessage) {
            return addErrorMessage(
                'Server error',
                'Server error occurred. Please refresh the page if the issue persists.'
            );
        }

        function handleStreamingError(error, context, userMessage = null) {
            clearStreamingTimeout();
            hideTypingIndicator();

            console.error(`Streaming error (${context}):`, error);

            // Show server error message
            addServerErrorMessage(userMessage);
            updateStatus('Server error occurred. Ready for new messages.', 'error');
            retryCount = 0; // Reset retry count
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isStreaming) {
                console.log('Blocked duplicate request - isStreaming:', isStreaming);
                return;
            }

            // Clear input and reset retry count for new messages
            messageInput.value = '';
            messageInput.style.height = 'auto';
            retryCount = 0;

            await sendMessageInternal(message, false);
        }

        async function sendMessageInternal(message, isRetry = false) {
            // Double-check to prevent race conditions
            if (isStreaming && !isRetry) {
                console.log('Blocked duplicate sendMessageInternal - isStreaming:', isStreaming);
                return;
            }

            // Add user message only if this is not a retry
            if (!isRetry) {
                addMessage(message, 'user');
            }

            // Set loading state
            setInputState(true);
            updateStatus(isRetry ? 'Retrying...' : 'Sending message...', 'normal');

            const typingIndicator = showTypingIndicator();

            try {
                // Set up timeout for the entire streaming operation
                setStreamingTimeout(() => {
                    throw new Error('Streaming timeout: Server took too long to respond');
                });

                // Start streaming request
                // Include sessionId in the request if available
                let url = `${API_BASE}?apikey=${encodeURIComponent(API_KEY)}&query=${encodeURIComponent(message)}&stream=true`;
                if (sessionId) {
                    url += `&sessionId=${encodeURIComponent(sessionId)}`;
                    console.log(`Using existing session: ${sessionId}`);
                } else {
                    console.log('Creating new session');
                }

                // Add AbortController for request cancellation
                const abortController = new AbortController();
                const timeoutId = setTimeout(() => abortController.abort(), STREAMING_TIMEOUT);

                const response = await fetch(url, {
                    signal: abortController.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorText = await response.text().catch(() => 'Unknown error');
                    throw new Error(`HTTP ${response.status}: ${response.statusText}. ${errorText}`);
                }

                // Check if response body exists
                if (!response.body) {
                    throw new Error('No response body received from server');
                }

                hideTypingIndicator();

                // Create initial bot message for streaming
                const botMessageContent = addMessage('', 'bot', true);
                updateStatus('Receiving response...', 'normal');

                // Handle streaming response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let accumulatedText = '';
                let lastActivityTime = Date.now();
                let hasReceivedData = false;

                // Reset timeout for streaming data
                clearStreamingTimeout();

                while (true) {
                    try {
                        // Set timeout for each read operation
                        const readPromise = reader.read();
                        const timeoutPromise = new Promise((_, reject) => {
                            setTimeout(() => reject(new Error('Stream read timeout')), 10000);
                        });

                        const { done, value } = await Promise.race([readPromise, timeoutPromise]);

                        if (done) {
                            if (!hasReceivedData) {
                                throw new Error('Stream ended without receiving any data');
                            }
                            break;
                        }

                        hasReceivedData = true;
                        lastActivityTime = Date.now();

                        if (!value || value.length === 0) {
                            continue; // Skip empty chunks
                        }

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // Keep incomplete line in buffer

                        for (const line of lines) {
                            if (line.trim() === '') continue; // Skip empty lines

                            if (line.startsWith('data: ')) {
                                try {
                                    const dataStr = line.slice(6).trim();
                                    if (dataStr === '[DONE]') {
                                        // Some APIs send [DONE] to indicate completion
                                        break;
                                    }

                                    const data = JSON.parse(dataStr);

                                    if (data.type === 'error') {
                                        throw new Error(data.message || 'Server reported an error');
                                    } else if (data.type === 'session') {
                                        // Store session ID for future requests
                                        sessionId = data.sessionId;
                                        console.log(`Session established: ${sessionId}`);
                                        updateStatus('Session established • Receiving response...', 'normal');
                                    } else if (data.type === 'content') {
                                        accumulatedText += data.content;

                                        // Format and display the accumulated text
                                        const formattedHtml = formatMarkdownToHtml(accumulatedText);
                                        botMessageContent.innerHTML = formattedHtml;

                                        // Trigger MathJax rendering for new content
                                        if (window.MathJax && window.MathJax.typesetPromise) {
                                            try {
                                                window.MathJax.typesetPromise([botMessageContent]);
                                            } catch (err) {
                                                console.warn('MathJax rendering error:', err);
                                            }
                                        }

                                        scrollToBottom();
                                    } else if (data.type === 'done') {
                                        // Final formatting pass
                                        const finalFormattedHtml = formatMarkdownToHtml(accumulatedText);
                                        botMessageContent.innerHTML = finalFormattedHtml;

                                        // Final MathJax rendering
                                        if (window.MathJax && window.MathJax.typesetPromise) {
                                            try {
                                                window.MathJax.typesetPromise([botMessageContent]);
                                            } catch (err) {
                                                console.warn('MathJax rendering error:', err);
                                            }
                                        }

                                        updateStatus('Response complete', 'success');
                                        setTimeout(() => {
                                            const memoryStatus = sessionId ? 'Conversation memory active' : `Connected to ${CONFIG.serviceName}`;
                                            updateStatus(`Ready to chat • ${memoryStatus}`, 'normal');
                                        }, 2000);
                                        break;
                                    }
                                } catch (parseError) {
                                    console.warn('Failed to parse SSE data:', line, parseError);
                                    // Continue processing other lines instead of failing completely
                                }
                            }
                        }

                        // Check for stream timeout (no activity for too long)
                        if (Date.now() - lastActivityTime > STREAMING_TIMEOUT) {
                            throw new Error('Stream timeout: No data received for too long');
                        }

                    } catch (readError) {
                        if (readError.name === 'AbortError') {
                            throw new Error('Stream was cancelled');
                        }
                        throw readError;
                    }
                }

                // Ensure we have some content
                if (!accumulatedText.trim()) {
                    throw new Error('No content received from the AI service');
                }

            } catch (error) {
                // Use the comprehensive error handling system
                handleStreamingError(error, 'Streaming request failed', message);
            } finally {
                clearStreamingTimeout();
                setInputState(false);
                currentStreamingMessage = null;
                messageInput.focus();
            }
        }

        // Add fallback function for non-streaming requests
        async function sendMessageFallback(message) {
            try {
                updateStatus('Falling back to non-streaming mode...', 'normal');

                let url = `${API_BASE}?apikey=${encodeURIComponent(API_KEY)}&query=${encodeURIComponent(message)}`;
                if (sessionId) {
                    url += `&sessionId=${encodeURIComponent(sessionId)}`;
                }

                const response = await fetch(url);

                if (!response.ok) {
                    const errorText = await response.text().catch(() => 'Unknown error');
                    throw new Error(`HTTP ${response.status}: ${response.statusText}. ${errorText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // Add the response
                if (data.response) {
                    const formattedHtml = formatMarkdownToHtml(data.response);
                    addMessage('', 'bot', false, true);
                    const botMessageContent = messagesContainer.lastElementChild.querySelector('.message-content');
                    botMessageContent.innerHTML = formattedHtml;

                    // Trigger MathJax rendering
                    if (window.MathJax && window.MathJax.typesetPromise) {
                        try {
                            window.MathJax.typesetPromise([botMessageContent]);
                        } catch (err) {
                            console.warn('MathJax rendering error:', err);
                        }
                    }

                    updateStatus('Response complete (non-streaming)', 'success');
                    setTimeout(() => {
                        const memoryStatus = sessionId ? 'Conversation memory active' : `Connected to ${CONFIG.serviceName}`;
                        updateStatus(`Ready to chat • ${memoryStatus}`, 'normal');
                    }, 2000);
                } else {
                    throw new Error('No response received from server');
                }

            } catch (error) {
                console.error('Fallback request failed:', error);
                const errorMessage = getErrorMessage(error, 'Fallback request failed');
                addMessage(`❌ ${errorMessage}`, 'bot', false, false);
                updateStatus('Both streaming and fallback failed. Please try again.', 'error');
            }
        }



        // Enhanced error recovery
        function resetErrorState() {
            retryCount = 0;
            clearStreamingTimeout();
            hideTypingIndicator();
            setInputState(false);
        }

        // Add keyboard shortcut for clearing errors
        document.addEventListener('keydown', function (e) {
            // Ctrl+R or Cmd+R to reset error state
            if ((e.ctrlKey || e.metaKey) && e.key === 'r' && !isStreaming) {
                e.preventDefault();
                resetErrorState();
                updateStatus('Error state cleared. Ready to try again.', 'success');
                setTimeout(() => {
                    updateStatus(`Ready to chat • Connected to ${CONFIG.serviceName}`, 'normal');
                }, 2000);
            }
        });

        // Focus input on load
        window.addEventListener('load', () => {
            applyConfiguration();
            messageInput.focus();
        });

        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            clearStreamingTimeout();
        });

        // Clear session button functionality
        document.getElementById('clearSessionButton').addEventListener('click', clearSession);

        // Notify parent window when embedded (for widget integration)
        if (EMBEDDED && window.parent !== window) {
            // Send ready message to parent
            window.parent.postMessage({
                type: 'chatReady',
                config: CONFIG
            }, '*');

            // Listen for configuration from parent
            window.addEventListener('message', function (event) {
                if (event.data.type === 'configure') {
                    // Handle configuration from parent widget
                    if (event.data.apiKey) {
                        API_KEY = event.data.apiKey;
                    }

                    // Update configuration
                    if (event.data.config) {
                        Object.assign(CONFIG, event.data.config);
                        applyConfiguration();
                    }
                }
            });
        }

        // Global function to update configuration (for external use)
        window.updateChatConfig = function (newConfig) {
            Object.assign(CONFIG, newConfig);
            applyConfiguration();
        };

        // Global function to get current configuration
        window.getChatConfig = function () {
            return { ...CONFIG };
        };
    </script>
</body>

</html>